# SOCKS5代理设置指南 (proxy-seller.com)

## 🔧 环境准备

### 1. 安装必要的Python包
```bash
pip install requests[socks] PySocks
```

### 2. 验证安装
运行以下命令验证包是否正确安装：
```bash
python -c "import socks, requests; print('✅ 安装成功')"
```

## 📝 代理配置

### 1. 获取代理信息
从proxy-seller.com获取您的代理信息，格式通常为：
- **服务器地址**: 如 `***********`
- **端口**: 如 `50101`
- **用户名**: 如 `10LZWF`
- **密码**: 如 `tMuNS7kDdq`

### 2. 修改代码中的代理信息
在 `main.py` 文件中找到以下部分并修改：
```python
# 请在这里填入您从proxy-seller.com获得的真实代理信息
proxy_host = "***********"      # 替换为您的代理服务器地址
proxy_port = 50101              # 替换为您的代理端口
proxy_username = "10LZWF"       # 替换为您的用户名
proxy_password = "tMuNS7kDdq"   # 替换为您的密码
```

## 🚨 常见问题排查

### 1. 连接失败的可能原因
- ❌ **代理信息错误**: 检查服务器地址、端口、用户名、密码
- ❌ **账户过期**: 登录proxy-seller.com检查账户状态
- ❌ **IP白名单**: 某些代理需要添加您的IP到白名单
- ❌ **网络限制**: 防火墙或ISP可能阻止代理连接
- ❌ **代理服务器故障**: 联系proxy-seller.com客服

### 2. 认证失败的解决方法
- 确认用户名和密码正确（区分大小写）
- 检查账户是否已激活
- 确认代理套餐是否支持SOCKS5协议

### 3. 网络连接问题
- 尝试ping代理服务器地址
- 检查本地防火墙设置
- 尝试使用不同的网络环境

## 🔍 测试步骤

1. **运行诊断工具**:
   ```bash
   python main.py
   ```

2. **查看测试结果**:
   - ✅ 绿色表示测试通过
   - ❌ 红色表示测试失败
   - ⚠️ 黄色表示警告

3. **根据结果采取行动**:
   - 如果基本连通性失败：检查网络和代理信息
   - 如果认证失败：检查用户名密码
   - 如果请求失败：检查代理协议支持

## 📞 获取帮助

如果问题仍然存在：
1. 联系proxy-seller.com客服
2. 提供详细的错误信息
3. 说明您的网络环境和使用场景
