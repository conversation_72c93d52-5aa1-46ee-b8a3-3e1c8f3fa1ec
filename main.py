import socket
import socks
import requests
import time
import sys
from urllib.parse import urlparse

# 设置控制台编码为UTF-8
if sys.platform == 'win32':
    import os
    os.system('chcp 65001 > nul')

def test_basic_connectivity(host, port):
    """
    测试基本网络连通性
    """
    print(f"测试基本连通性: {host}:{port}")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((host, port))
        sock.close()

        if result == 0:
            print(f"[成功] 可以连接到 {host}:{port}")
            return True
        else:
            print(f"[失败] 无法连接到 {host}:{port} (错误代码: {result})")
            return False
    except Exception as e:
        print(f"[失败] 连接测试出错: {e}")
        return False

def test_socks5_proxy_enhanced():
    """
    增强的SOCKS5代理测试 - 包含详细诊断
    """
    # 请在这里填入您从proxy-seller.com获得的真实代理信息
    proxy_host = "***********"  # 替换为您的代理服务器地址
    proxy_port = 50101          # 替换为您的代理端口
    proxy_username = "10LZWF"   # 替换为您的用户名
    proxy_password = "tMuNS7kDdq"  # 替换为您的密码

    print(f"测试SOCKS5代理: {proxy_host}:{proxy_port}")
    print(f"用户名: {proxy_username}")
    print("-" * 50)

    # 步骤1: 测试基本网络连通性
    print("\n1. 测试基本网络连通性...")
    if not test_basic_connectivity(proxy_host, proxy_port):
        print("❌ 基本连通性测试失败，请检查:")
        print("   - 代理服务器地址和端口是否正确")
        print("   - 网络连接是否正常")
        print("   - 防火墙是否阻止连接")
        return False

    # 步骤2: 测试SOCKS5认证
    print("\n2. 测试SOCKS5认证...")
    try:
        sock = socks.socksocket()
        sock.set_proxy(socks.SOCKS5, proxy_host, proxy_port, username=proxy_username, password=proxy_password)
        sock.settimeout(15)

        # 尝试连接到一个简单的服务器
        start_time = time.time()
        sock.connect(("httpbin.org", 80))
        connect_time = time.time() - start_time

        print(f"[成功] SOCKS5认证成功! 连接时间: {connect_time:.2f}秒")
        sock.close()

    except socks.ProxyConnectionError as e:
        print(f"[失败] SOCKS5连接错误: {e}")
        print("可能的原因:")
        print("   - 代理服务器地址或端口错误")
        print("   - 代理服务器不可用")
        return False
    except socks.SOCKS5AuthError as e:
        print(f"[失败] SOCKS5认证错误: {e}")
        print("可能的原因:")
        print("   - 用户名或密码错误")
        print("   - 代理账户已过期或被禁用")
        return False
    except Exception as e:
        print(f"[失败] SOCKS5测试失败: {e}")
        return False

    # 步骤3: 测试HTTP请求
    print("\n3. 测试通过SOCKS5的HTTP请求...")
    try:
        # 构建SOCKS5代理URL
        proxy_url = f"socks5://{proxy_username}:{proxy_password}@{proxy_host}:{proxy_port}"
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }

        start_time = time.time()
        response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=20)
        request_time = time.time() - start_time

        if response.status_code == 200:
            ip_info = response.json()
            print(f"[成功] HTTP请求成功! 响应时间: {request_time:.2f}秒")
            print(f"  代理IP: {ip_info.get('origin', '未知')}")
        else:
            print(f"[失败] HTTP请求失败: 状态码 {response.status_code}")
            return False

    except requests.exceptions.ProxyError as e:
        print(f"[失败] 代理错误: {e}")
        print("可能的原因:")
        print("   - 代理服务器拒绝连接")
        print("   - 认证信息错误")
        return False
    except requests.exceptions.Timeout as e:
        print(f"[失败] 请求超时: {e}")
        print("可能的原因:")
        print("   - 代理服务器响应慢")
        print("   - 网络连接不稳定")
        return False
    except Exception as e:
        print(f"[失败] HTTP请求失败: {e}")
        return False

    # 步骤4: 测试HTTPS请求
    print("\n4. 测试通过SOCKS5的HTTPS请求...")
    try:
        start_time = time.time()
        response = requests.get('https://httpbin.org/ip', proxies=proxies, timeout=20)
        request_time = time.time() - start_time

        if response.status_code == 200:
            ip_info = response.json()
            print(f"[成功] HTTPS请求成功! 响应时间: {request_time:.2f}秒")
            print(f"  代理IP: {ip_info.get('origin', '未知')}")
        else:
            print(f"[失败] HTTPS请求失败: 状态码 {response.status_code}")
            return False

    except Exception as e:
        print(f"[失败] HTTPS请求失败: {e}")
        return False

    # 步骤5: IP地址验证
    print("\n5. 验证IP地址变化...")
    try:
        # 获取本地IP
        print("  获取本地IP...")
        local_response = requests.get('http://httpbin.org/ip', timeout=10)
        if local_response.status_code == 200:
            local_ip = local_response.json().get('origin', '未知')
            print(f"  本地IP: {local_ip}")

            # 通过代理获取IP
            print("  通过代理获取IP...")
            proxy_response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=20)
            if proxy_response.status_code == 200:
                proxy_ip = proxy_response.json().get('origin', '未知')

                if local_ip != proxy_ip:
                    print(f"[成功] ✅ IP地址已更改，SOCKS5代理工作正常!")
                    print(f"  本地IP: {local_ip}")
                    print(f"  代理IP: {proxy_ip}")
                else:
                    print(f"[警告] ⚠️ IP地址未更改，代理可能未生效")
                    print(f"  IP地址: {local_ip}")

    except Exception as e:
        print(f"[警告] IP验证失败: {e}")

    print("\n" + "=" * 50)
    print("[完成] ✅ SOCKS5代理测试完成!")
    return True

def check_proxy_requirements():
    """
    检查代理使用的必要条件
    """
    print("🔍 检查代理使用环境...")
    print("-" * 50)

    # 检查必要的库
    try:
        import socks
        print("✅ PySocks库已安装")
    except ImportError:
        print("❌ PySocks库未安装")
        print("   请运行: pip install PySocks")
        return False

    try:
        import requests
        print("✅ Requests库已安装")
    except ImportError:
        print("❌ Requests库未安装")
        print("   请运行: pip install requests")
        return False

    # 检查requests[socks]支持
    try:
        import requests
        response = requests.get('http://httpbin.org/ip',
                              proxies={'http': 'socks5://test:test@127.0.0.1:1080'},
                              timeout=1)
    except requests.exceptions.InvalidSchema:
        print("❌ Requests库不支持SOCKS代理")
        print("   请运行: pip install requests[socks]")
        return False
    except:
        print("✅ Requests库支持SOCKS代理")

    return True



def main():
    """
    主函数：SOCKS5代理诊断和测试工具
    """
    print("=" * 60)
    print("🔧 SOCKS5代理诊断工具 (proxy-seller.com)")
    print("=" * 60)

    # 检查环境要求
    print("\n� 步骤1: 检查环境要求")
    if not check_proxy_requirements():
        print("\n❌ 环境检查失败，请先安装必要的依赖包")
        return

    print("\n" + "=" * 60)

    # 测试SOCKS5代理
    print("\n🔗 步骤2: 测试SOCKS5代理连接")
    print("=" * 60)

    print("\n⚠️  重要提示:")
    print("1. 请确保您已在代码中填入正确的代理信息")
    print("2. 代理信息格式: 服务器地址:端口:用户名:密码")
    print("3. 确保代理账户有效且未过期")
    print("4. 检查您的网络是否允许连接到代理服务器")
    print("-" * 60)

    try:
        socks5_result = test_socks5_proxy_enhanced()
    except Exception as e:
        print(f"SOCKS5代理测试出错: {e}")
        socks5_result = False

    # 总结和建议
    print("\n" + "=" * 60)
    print("📊 测试结果和建议")
    print("=" * 60)

    if socks5_result:
        print("✅ SOCKS5代理测试成功!")
        print("\n🎉 您的代理配置正确，可以正常使用")
    else:
        print("❌ SOCKS5代理测试失败")
        print("\n🔧 故障排除建议:")
        print("1. 检查代理服务器信息是否正确")
        print("2. 确认用户名和密码是否正确")
        print("3. 检查代理账户是否已过期")
        print("4. 尝试联系proxy-seller.com客服")
        print("5. 检查本地网络和防火墙设置")
        print("6. 确保已安装: pip install requests[socks] PySocks")

    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
