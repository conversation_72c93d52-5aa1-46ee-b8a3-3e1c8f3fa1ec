import socket
import socks
import requests
import time
import sys
from urllib.parse import urlparse

# 设置控制台编码为UTF-8
if sys.platform == 'win32':
    import os
    os.system('chcp 65001 > nul')

def test_http_proxy():
    """
    测试HTTP/HTTPS代理连通性 (端口50100)
    """
    # HTTP代理配置
    proxy_url = "******************************************"

    # 解析代理URL
    parsed = urlparse(proxy_url)
    proxy_host = parsed.hostname
    proxy_port = parsed.port
    proxy_username = parsed.username
    proxy_password = parsed.password

    print(f"测试HTTP代理: {proxy_host}:{proxy_port}")
    print(f"用户名: {proxy_username}")
    print("-" * 50)

    # 测试1: HTTP请求测试
    print("\n1. 正在测试HTTP请求...")
    try:
        # 配置requests使用HTTP代理
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }

        start_time = time.time()
        response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=15)
        request_time = time.time() - start_time

        if response.status_code == 200:
            ip_info = response.json()
            print(f"[成功] HTTP请求成功! 响应时间: {request_time:.2f}秒")
            print(f"  代理IP: {ip_info.get('origin', '未知')}")
        else:
            print(f"[失败] HTTP请求失败: 状态码 {response.status_code}")
            return False

    except Exception as e:
        print(f"[失败] HTTP请求失败: {e}")
        return False

    # 测试2: HTTPS请求测试
    print("\n2. 正在测试HTTPS请求...")
    try:
        start_time = time.time()
        response = requests.get('https://httpbin.org/ip', proxies=proxies, timeout=15)
        request_time = time.time() - start_time

        if response.status_code == 200:
            ip_info = response.json()
            print(f"[成功] HTTPS请求成功! 响应时间: {request_time:.2f}秒")
            print(f"  代理IP: {ip_info.get('origin', '未知')}")
        else:
            print(f"[失败] HTTPS请求失败: 状态码 {response.status_code}")
            return False

    except Exception as e:
        print(f"[失败] HTTPS请求失败: {e}")
        return False

    # 测试3: 获取真实IP对比
    print("\n3. 正在获取本地IP进行对比...")
    try:
        # 不使用代理获取本地IP
        local_response = requests.get('http://httpbin.org/ip', timeout=10)
        if local_response.status_code == 200:
            local_ip = local_response.json().get('origin', '未知')
            print(f"  本地IP: {local_ip}")

            # 通过代理获取IP
            proxy_response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=15)
            if proxy_response.status_code == 200:
                proxy_ip = proxy_response.json().get('origin', '未知')

                if local_ip != proxy_ip:
                    print(f"[成功] IP地址已更改，代理工作正常")
                    print(f"  本地IP: {local_ip}")
                    print(f"  代理IP: {proxy_ip}")
                else:
                    print(f"[警告] IP地址未更改，可能代理未生效")

    except Exception as e:
        print(f"[警告] IP对比测试失败: {e}")

    print("\n" + "=" * 50)
    print("[完成] HTTP代理连通性测试完成!")
    return True

def test_socks5_proxy():
    """
    测试SOCKS5代理连通性 (端口50101)
    """
    # SOCKS5代理配置
    proxy_url = "socks5://10LZWF:tMuNS7kDdq@***********:50101"

    # 解析代理URL
    parsed = urlparse(proxy_url)
    proxy_host = parsed.hostname
    proxy_port = parsed.port
    proxy_username = parsed.username
    proxy_password = parsed.password

    print(f"测试SOCKS5代理: {proxy_host}:{proxy_port}")
    print(f"用户名: {proxy_username}")
    print("-" * 50)

    # 测试1: 基本socket连接测试
    print("\n1. 正在测试基本socket连接...")
    try:
        # 创建socket并设置SOCKS5代理
        sock = socks.socksocket()
        sock.set_proxy(socks.SOCKS5, proxy_host, proxy_port, username=proxy_username, password=proxy_password)

        # 尝试连接到测试服务器
        sock.settimeout(10)
        start_time = time.time()
        sock.connect(("httpbin.org", 80))
        connect_time = time.time() - start_time

        print(f"[成功] Socket连接成功! 连接时间: {connect_time:.2f}秒")
        sock.close()

    except Exception as e:
        print(f"[失败] Socket连接失败: {e}")
        return False

    # 测试2: HTTP请求测试
    print("\n2. 正在测试HTTP请求...")
    try:
        # 配置requests使用SOCKS5代理
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }

        start_time = time.time()
        response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=15)
        request_time = time.time() - start_time

        if response.status_code == 200:
            ip_info = response.json()
            print(f"[成功] HTTP请求成功! 响应时间: {request_time:.2f}秒")
            print(f"  代理IP: {ip_info.get('origin', '未知')}")
        else:
            print(f"[失败] HTTP请求失败: 状态码 {response.status_code}")
            return False

    except Exception as e:
        print(f"[失败] HTTP请求失败: {e}")
        return False

    # 测试3: HTTPS请求测试
    print("\n3. 正在测试HTTPS请求...")
    try:
        start_time = time.time()
        response = requests.get('https://httpbin.org/ip', proxies=proxies, timeout=15)
        request_time = time.time() - start_time

        if response.status_code == 200:
            ip_info = response.json()
            print(f"[成功] HTTPS请求成功! 响应时间: {request_time:.2f}秒")
            print(f"  代理IP: {ip_info.get('origin', '未知')}")
        else:
            print(f"[失败] HTTPS请求失败: 状态码 {response.status_code}")
            return False

    except Exception as e:
        print(f"[失败] HTTPS请求失败: {e}")
        return False

    # 测试4: 获取真实IP对比
    print("\n4. 正在获取本地IP进行对比...")
    try:
        # 不使用代理获取本地IP
        local_response = requests.get('http://httpbin.org/ip', timeout=10)
        if local_response.status_code == 200:
            local_ip = local_response.json().get('origin', '未知')
            print(f"  本地IP: {local_ip}")

            # 通过代理获取IP
            proxy_response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=15)
            if proxy_response.status_code == 200:
                proxy_ip = proxy_response.json().get('origin', '未知')

                if local_ip != proxy_ip:
                    print(f"[成功] IP地址已更改，代理工作正常")
                    print(f"  本地IP: {local_ip}")
                    print(f"  代理IP: {proxy_ip}")
                else:
                    print(f"[警告] IP地址未更改，可能代理未生效")

    except Exception as e:
        print(f"[警告] IP对比测试失败: {e}")

    print("\n" + "=" * 50)
    print("[完成] SOCKS5代理连通性测试完成!")
    return True

if __name__ == "__main__":
    try:
        test_socks5_proxy()
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
